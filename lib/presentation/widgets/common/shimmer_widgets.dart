import 'package:flutter/material.dart';
import 'loading_widget.dart';

// Dashboard shimmer
class DashboardShimmer extends StatelessWidget {
  const DashboardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome text shimmer
          const ShimmerLine(width: 200, height: 32),
          const SizedBox(height: 16),
          
          // Dashboard cards shimmer
          Row(
            children: [
              Expanded(
                child: _DashboardCardShimmer(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _DashboardCardShimmer(),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Recent activity card shimmer
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const ShimmerLine(width: 150, height: 20),
                        const ShimmerLine(width: 60, height: 16),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Activity items
                    Expanded(
                      child: ListView.separated(
                        itemCount: 5,
                        separatorBuilder: (context, index) => const SizedBox(height: 12),
                        itemBuilder: (context, index) => const ActivityItemShimmer(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _DashboardCardShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const ShimmerCircle(size: 40),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const ShimmerLine(height: 18),
                      const SizedBox(height: 8),
                      const ShimmerLine(width: 120, height: 14),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Resume card shimmer - improved to match actual resume card layout
class ResumeCardShimmer extends StatelessWidget {
  const ResumeCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with title and menu
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Resume title
                      const ShimmerLine(
                        width: 180,
                        height: 22,
                      ),
                      const SizedBox(height: 6),
                      // Resume subtitle
                      const ShimmerLine(
                        width: 140,
                        height: 16,
                      ),
                    ],
                  ),
                ),
                // Menu button
                const ShimmerBox(
                  width: 24,
                  height: 24,
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Status and date row
            Row(
              children: [
                // Status icon
                const ShimmerCircle(size: 16),
                const SizedBox(width: 8),
                // Status text
                const ShimmerLine(
                  width: 80,
                  height: 14,
                ),
                const Spacer(),
                // Date text
                const ShimmerLine(
                  width: 100,
                  height: 14,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Template card shimmer - improved design
class TemplateCardShimmer extends StatelessWidget {
  const TemplateCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Template preview area
          Expanded(
            flex: 3,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: const ShimmerBox(
                width: double.infinity,
                height: double.infinity,
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
            ),
          ),

          // Template info area
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Template name
                  const ShimmerLine(
                    width: 120,
                    height: 16,
                  ),
                  const SizedBox(height: 8),
                  // Template category
                  const ShimmerLine(
                    width: 80,
                    height: 12,
                  ),
                  const SizedBox(height: 8),
                  // Bottom row with price and favorite
                  Row(
                    children: [
                      const ShimmerBox(
                        width: 60,
                        height: 20,
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      const Spacer(),
                      const ShimmerCircle(size: 20),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Activity item shimmer - improved layout
class ActivityItemShimmer extends StatelessWidget {
  const ActivityItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const ShimmerCircle(size: 40),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Activity title
              const ShimmerLine(
                width: 180,
                height: 16,
              ),
              const SizedBox(height: 6),
              // Activity description
              const ShimmerLine(
                width: 140,
                height: 12,
              ),
            ],
          ),
        ),
        // Time stamp
        const ShimmerLine(
          width: 60,
          height: 12,
        ),
      ],
    );
  }
}

// Form section shimmer
class FormSectionShimmer extends StatelessWidget {
  const FormSectionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ShimmerLine(width: 150, height: 20),
          const SizedBox(height: 16),
          const ShimmerBox(width: double.infinity, height: 56),
          const SizedBox(height: 16),
          const ShimmerBox(width: double.infinity, height: 56),
          const SizedBox(height: 16),
          const ShimmerBox(width: double.infinity, height: 100),
        ],
      ),
    );
  }
}

// Resume list shimmer
class ResumeListShimmer extends StatelessWidget {
  const ResumeListShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 6,
      itemBuilder: (context, index) => const ResumeCardShimmer(),
    );
  }
}

// Template grid shimmer
class TemplateGridShimmer extends StatelessWidget {
  const TemplateGridShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: 8,
      itemBuilder: (context, index) => const TemplateCardShimmer(),
    );
  }
}

// Activity list shimmer
class ActivityListShimmer extends StatelessWidget {
  const ActivityListShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: 10,
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      itemBuilder: (context, index) => const ActivityItemShimmer(),
    );
  }
}

// Resume builder shimmer
class ResumeBuilderShimmer extends StatelessWidget {
  const ResumeBuilderShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 8,
      child: Scaffold(
        appBar: AppBar(
          title: const ShimmerLine(width: 150, height: 20),
          actions: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ShimmerCircle(size: 40),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ShimmerCircle(size: 40),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ShimmerCircle(size: 40),
            ),
          ],
          bottom: TabBar(
            isScrollable: true,
            tabs: [
              Tab(child: ShimmerLine(width: 60, height: 16)),
              Tab(child: ShimmerLine(width: 70, height: 16)),
              Tab(child: ShimmerLine(width: 90, height: 16)),
              Tab(child: ShimmerLine(width: 80, height: 16)),
              Tab(child: ShimmerLine(width: 50, height: 16)),
              Tab(child: ShimmerLine(width: 70, height: 16)),
              Tab(child: ShimmerLine(width: 100, height: 16)),
              Tab(child: ShimmerLine(width: 80, height: 16)),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            FormSectionShimmer(),
            FormSectionShimmer(),
            FormSectionShimmer(),
            FormSectionShimmer(),
            FormSectionShimmer(),
            FormSectionShimmer(),
            FormSectionShimmer(),
            FormSectionShimmer(),
          ],
        ),
        floatingActionButton: const ShimmerCircle(size: 56),
      ),
    );
  }
}
